"use strict";
const { Model } = require("sequelize");
const attributes = require("../attributes/admins-attributes");
const { timestamps } = require("../config/common-options");
const { AGENCY_USER } = require("../config/table-names");

module.exports = (sequelize, DataTypes) => {
  class AgencyUser extends Model {
    static associate(models) {
      // define association here
    }
  }
  AgencyUser.init(attributes, {
    sequelize,
    modelName: "AgencyUser",
    tableName: AGENCY_USER,
    ...timestamps,
  });
  return Admins;
};
