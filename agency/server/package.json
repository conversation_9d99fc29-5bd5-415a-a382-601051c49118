{"name": "agency-server", "version": "1.0.0", "description": "Agency Express Server", "main": "dist/index.js", "type": "commonjs", "scripts": {"build": "tsc && cp -r dist/agency/server/* dist/ && rm -rf dist/agency && rm -rf dist/web", "start": "node dist/index.js", "dev": "ts-node index.ts", "dev:watch": "nodemon --exec ts-node index.ts", "start:js": "node index.js", "dev:js": "nodemon index.js"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-fileupload": "^1.4.3", "helmet": "^7.1.0", "http-status": "^1.7.4", "morgan": "^1.10.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "storeseo-enums": "workspace:^", "storeseo-logger": "workspace:^", "storeseo-schema": "workspace:^", "xss-filters": "^1.2.7", "yup": "^1.4.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/express-fileupload": "^1.5.1", "@types/helmet": "^4.0.0", "@types/morgan": "^1.9.10", "@types/passport": "^1.0.17", "@types/passport-jwt": "^4.0.1", "@types/xss-filters": "^1.2.0", "ts-node": "^10.9.2"}}